import {
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
  FileTextOutlined,
  TeamOutlined,
  ProfileOutlined,
  LogoutOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import type { RouteMapping, MenuItemConfig } from './types';

/**
 * 路由与标签页的映射关系
 */
export const ROUTE_TAB_MAPPING: RouteMapping = {
  '/': { key: 'dashboard', label: '仪表板' },
  '/dashboard': { key: 'dashboard', label: '仪表板' },
  '/users': { key: 'users', label: '用户管理' },
  '/tasks': { key: 'tasks', label: '任务管理' },
  '/teams': { key: 'teams', label: '团队协作' },
};

/**
 * 标签页标题映射
 */
export const TAB_LABELS: Record<string, string> = {
  dashboard: '仪表板',
  users: '用户管理',
  tasks: '任务管理',
  teams: '团队协作',
};

/**
 * 侧边栏菜单配置
 */
export const SIDEBAR_MENU_ITEMS: MenuProps['items'] = [
  {
    key: 'dashboard',
    icon: <DashboardOutlined />,
    label: '仪表板',
  },
  {
    key: 'users',
    icon: <UserOutlined />,
    label: '用户管理',
  },
  {
    key: 'tasks',
    icon: <FileTextOutlined />,
    label: '任务管理',
  },
  {
    key: 'teams',
    icon: <TeamOutlined />,
    label: '团队协作',
  },
  {
    key: 'sub1',
    label: '系统设置',
    icon: <SettingOutlined />,
    children: [
      {
        key: 'settings-basic',
        label: '基础设置',
      },
      {
        key: 'settings-permission',
        label: '权限管理',
      },
      {
        key: 'settings-logs',
        label: '系统日志',
      },
    ],
  },
];

/**
 * 用户下拉菜单配置
 */
export const USER_MENU_ITEMS: MenuProps['items'] = [
  {
    key: 'profile',
    icon: <ProfileOutlined />,
    label: '个人资料',
  },
  {
    key: 'settings',
    icon: <SettingOutlined />,
    label: '账户设置',
  },
  {
    type: 'divider',
  },
  {
    key: 'logout',
    icon: <LogoutOutlined />,
    label: '退出登录',
    danger: true,
  },
];

/**
 * 默认用户信息
 */
export const DEFAULT_USER = {
  name: '管理员',
  avatar: undefined,
  role: 'admin',
};

/**
 * 布局常量
 */
export const LAYOUT_CONSTANTS = {
  SIDER_WIDTH: 240,
  SIDER_COLLAPSED_WIDTH: 48,
  HEADER_HEIGHT: 48,
  TABS_HEIGHT: 30,
  CONTENT_MARGIN: 12,
  LOGO_TEXT: 'DB SQL MONITOR',
  LOGO_TEXT_COLLAPSED: 'S',
} as const;
