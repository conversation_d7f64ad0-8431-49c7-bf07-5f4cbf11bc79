import React from 'react';
import { Layout, Menu } from 'antd';
import type { SidebarProps } from '../types';
import { LAYOUT_CONSTANTS } from '../constants';
import styles from '../DefaultLayout.module.css';

const { Sider } = Layout;

/**
 * 侧边栏组件
 */
export const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  selectedKeys,
  menuItems,
  onMenuClick,
}) => {
  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={LAYOUT_CONSTANTS.SIDER_WIDTH}
      collapsedWidth={LAYOUT_CONSTANTS.SIDER_COLLAPSED_WIDTH}
      className={`${styles.sider} ${collapsed ? styles.siderCollapsed : ''}`}
    >
      {/* Logo 区域 */}
      <div className={`${styles.logo} ${collapsed ? styles.collapsed : styles.expanded}`}>
        <div className={`${styles.logoText} ${collapsed ? styles.collapsed : styles.expanded}`}>
          {collapsed ? LAYOUT_CONSTANTS.LOGO_TEXT_COLLAPSED : LAYOUT_CONSTANTS.LOGO_TEXT}
        </div>
      </div>

      {/* 菜单 */}
      <Menu
        theme="light"
        mode="inline"
        selectedKeys={selectedKeys}
        onClick={onMenuClick}
        items={menuItems}
        className={styles.menu}
      />
    </Sider>
  );
};
