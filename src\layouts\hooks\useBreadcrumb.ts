import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { Space } from 'antd';
import { HomeOutlined } from '@ant-design/icons';
import type { BreadcrumbItem } from '../types';

/**
 * 面包屑导航 Hook
 */
export const useBreadcrumb = (): BreadcrumbItem[] => {
  const location = useLocation();

  const breadcrumbItems = useMemo(() => {
    const path = location.pathname;
    const items: BreadcrumbItem[] = [
      {
        href: '/dashboard',
        title: (
          <Space>
            <HomeOutlined />
            <span>首页</span>
          </Space>
        ),
      },
    ];

    if (path === '/users') {
      items.push({
        href: '/users',
        title: <span>用户管理</span>,
      });
    } else if (path === '/tasks') {
      items.push({
        href: '/tasks',
        title: <span>任务管理</span>,
      });
    } else if (path === '/teams') {
      items.push({
        href: '/teams',
        title: <span>团队协作</span>,
      });
    }

    return items;
  }, [location.pathname]);

  return breadcrumbItems;
};
