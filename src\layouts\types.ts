import type { MenuProps } from 'antd';

/**
 * 标签页项目接口
 */
export interface TabItem {
  key: string;
  label: string;
  closable?: boolean;
}

/**
 * 菜单项配置接口
 */
export interface MenuItemConfig {
  key: string;
  label: string;
  path: string;
  icon?: React.ReactNode;
  children?: MenuItemConfig[];
}

/**
 * 路由映射接口
 */
export interface RouteMapping {
  [path: string]: {
    key: string;
    label: string;
  };
}

/**
 * 面包屑项目接口
 */
export interface BreadcrumbItem {
  href?: string;
  title: React.ReactNode;
}

/**
 * 布局上下文接口
 */
export interface LayoutContextType {
  contentHeight: number;
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
}

/**
 * Header 组件属性接口
 */
export interface HeaderProps {
  collapsed: boolean;
  onToggleCollapse: () => void;
  breadcrumbItems: BreadcrumbItem[];
  userMenuItems: MenuProps['items'];
}

/**
 * Sidebar 组件属性接口
 */
export interface SidebarProps {
  collapsed: boolean;
  selectedKeys: string[];
  menuItems: MenuProps['items'];
  onMenuClick: (info: { key: string }) => void;
}

/**
 * TabsContainer 组件属性接口
 */
export interface TabsContainerProps {
  activeTabKey: string;
  tabs: TabItem[];
  onTabChange: (key: string) => void;
  onTabRemove: (key: string) => void;
}

/**
 * 用户信息接口
 */
export interface UserInfo {
  name: string;
  avatar?: string;
  role?: string;
}
